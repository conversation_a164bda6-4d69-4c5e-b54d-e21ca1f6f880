{"version": 3, "file": "accessDeep.js", "sourceRoot": "", "sources": ["../src/accessDeep.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,aAAa,EAAE,KAAK,EAAE,MAAM,SAAS,CAAC;AAC/D,OAAO,EAAE,QAAQ,EAAE,MAAM,WAAW,CAAC;AAErC,MAAM,SAAS,GAAG,CAAC,KAA+B,EAAE,CAAS,EAAO,EAAE;IACpE,IAAI,CAAC,GAAG,KAAK,CAAC,IAAI;QAAE,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC;IAC3D,MAAM,IAAI,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC;IAC1B,OAAO,CAAC,GAAG,CAAC,EAAE;QACZ,IAAI,CAAC,IAAI,EAAE,CAAC;QACZ,CAAC,EAAE,CAAC;KACL;IAED,OAAO,IAAI,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC;AAC3B,CAAC,CAAC;AAEF,SAAS,YAAY,CAAC,IAAyB;IAC7C,IAAI,QAAQ,CAAC,IAAI,EAAE,WAAW,CAAC,EAAE;QAC/B,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAC;KAC3D;IACD,IAAI,QAAQ,CAAC,IAAI,EAAE,WAAW,CAAC,EAAE;QAC/B,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAC;KAC3D;IACD,IAAI,QAAQ,CAAC,IAAI,EAAE,aAAa,CAAC,EAAE;QACjC,MAAM,IAAI,KAAK,CAAC,0CAA0C,CAAC,CAAC;KAC7D;AACH,CAAC;AAED,MAAM,CAAC,MAAM,OAAO,GAAG,CAAC,MAAc,EAAE,IAAyB,EAAU,EAAE;IAC3E,YAAY,CAAC,IAAI,CAAC,CAAC;IAEnB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACpC,MAAM,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;QACpB,IAAI,KAAK,CAAC,MAAM,CAAC,EAAE;YACjB,MAAM,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC;SAClC;aAAM,IAAI,KAAK,CAAC,MAAM,CAAC,EAAE;YACxB,MAAM,GAAG,GAAG,CAAC,GAAG,CAAC;YACjB,MAAM,IAAI,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC;YAEhD,MAAM,QAAQ,GAAG,SAAS,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;YACxC,QAAQ,IAAI,EAAE;gBACZ,KAAK,KAAK;oBACR,MAAM,GAAG,QAAQ,CAAC;oBAClB,MAAM;gBACR,KAAK,OAAO;oBACV,MAAM,GAAG,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;oBAC9B,MAAM;aACT;SACF;aAAM;YACL,MAAM,GAAI,MAAc,CAAC,GAAG,CAAC,CAAC;SAC/B;KACF;IAED,OAAO,MAAM,CAAC;AAChB,CAAC,CAAC;AAEF,MAAM,CAAC,MAAM,OAAO,GAAG,CACrB,MAAW,EACX,IAAyB,EACzB,MAAuB,EAClB,EAAE;IACP,YAAY,CAAC,IAAI,CAAC,CAAC;IAEnB,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;QACrB,OAAO,MAAM,CAAC,MAAM,CAAC,CAAC;KACvB;IAED,IAAI,MAAM,GAAG,MAAM,CAAC;IAEpB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;QACxC,MAAM,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;QAEpB,IAAI,OAAO,CAAC,MAAM,CAAC,EAAE;YACnB,MAAM,KAAK,GAAG,CAAC,GAAG,CAAC;YACnB,MAAM,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;SACxB;aAAM,IAAI,aAAa,CAAC,MAAM,CAAC,EAAE;YAChC,MAAM,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;SACtB;aAAM,IAAI,KAAK,CAAC,MAAM,CAAC,EAAE;YACxB,MAAM,GAAG,GAAG,CAAC,GAAG,CAAC;YACjB,MAAM,GAAG,SAAS,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;SACjC;aAAM,IAAI,KAAK,CAAC,MAAM,CAAC,EAAE;YACxB,MAAM,KAAK,GAAG,CAAC,KAAK,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;YACpC,IAAI,KAAK,EAAE;gBACT,MAAM;aACP;YAED,MAAM,GAAG,GAAG,CAAC,GAAG,CAAC;YACjB,MAAM,IAAI,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC;YAEhD,MAAM,QAAQ,GAAG,SAAS,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;YACxC,QAAQ,IAAI,EAAE;gBACZ,KAAK,KAAK;oBACR,MAAM,GAAG,QAAQ,CAAC;oBAClB,MAAM;gBACR,KAAK,OAAO;oBACV,MAAM,GAAG,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;oBAC9B,MAAM;aACT;SACF;KACF;IAED,MAAM,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IAEtC,IAAI,OAAO,CAAC,MAAM,CAAC,EAAE;QACnB,MAAM,CAAC,CAAC,OAAO,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;KAC7C;SAAM,IAAI,aAAa,CAAC,MAAM,CAAC,EAAE;QAChC,MAAM,CAAC,OAAO,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC;KAC3C;IAED,IAAI,KAAK,CAAC,MAAM,CAAC,EAAE;QACjB,MAAM,QAAQ,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,OAAO,CAAC,CAAC;QAC7C,MAAM,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC;QAClC,IAAI,QAAQ,KAAK,QAAQ,EAAE;YACzB,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;YACxB,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;SACtB;KACF;IAED,IAAI,KAAK,CAAC,MAAM,CAAC,EAAE;QACjB,MAAM,GAAG,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QACnC,MAAM,QAAQ,GAAG,SAAS,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;QAExC,MAAM,IAAI,GAAG,CAAC,OAAO,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC;QAC9C,QAAQ,IAAI,EAAE;YACZ,KAAK,KAAK,CAAC,CAAC;gBACV,MAAM,MAAM,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC;gBAChC,MAAM,CAAC,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC;gBAEzC,IAAI,MAAM,KAAK,QAAQ,EAAE;oBACvB,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;iBACzB;gBACD,MAAM;aACP;YAED,KAAK,OAAO,CAAC,CAAC;gBACZ,MAAM,CAAC,GAAG,CAAC,QAAQ,EAAE,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;gBACnD,MAAM;aACP;SACF;KACF;IAED,OAAO,MAAM,CAAC;AAChB,CAAC,CAAC"}