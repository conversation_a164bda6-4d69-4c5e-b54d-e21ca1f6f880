<script setup>
import { ref, onMounted } from 'vue';
import { useVoterStore } from './stores/voterStore.js';
import { useCategoriesStore } from './stores/categoriesStore.js';
import { useTheme } from './composables/useTheme.js';

// Import components
import AppSidebar from './components/AppSidebar.vue';
import AppToolbar from './components/AppToolbar.vue';
import StatsCards from './components/StatsCards.vue';
import FilterPanel from './components/FilterPanel.vue';
import DataTable from './components/DataTable.vue';
// import VoterDetailPanel from './components/VoterDetailPanel.vue';
// import SettingsDashboard from './components/SettingsDashboard.vue';

// Initialize stores and composables
const voterStore = useVoterStore();
const categoriesStore = useCategoriesStore();
const { initializeTheme } = useTheme();

// Local state
const showFilters = ref(false);

// Initialize the application
onMounted(async () => {
  initializeTheme();
  await categoriesStore.initialize();
  await voterStore.initialize();
});
</script>

<template>
  <div id="app">
    <!-- Sidebar -->
    <AppSidebar />

    <!-- Main Content -->
    <main class="main-content">
      <!-- Toolbar -->
      <AppToolbar @toggle-filters="showFilters = !showFilters" />

      <!-- Statistics Cards -->
      <StatsCards />

      <!-- Filter Panel -->
      <FilterPanel :is-open="showFilters" />

      <!-- Data Table -->
      <DataTable />

      <!-- Temporary status -->
      <div class="temp-content" style="margin-top: 1rem;">
        <p>Remaining components to build:</p>
        <p>• Voter Detail Panel</p>
        <p>• Settings Dashboard</p>
      </div>
    </main>

    <!-- Future component:
    <VoterDetailPanel />
    -->
  </div>
</template>

<style>
/* Import CSS files */
@import './assets/normalize.css';
@import './assets/styles.css';

/* App-specific styles */
#app {
  width: 100%;
  height: 100vh;
  overflow: hidden;
}

/* Temporary styles for development */
.temp-content {
  padding: 2rem;
  text-align: center;
  background: var(--bg-secondary);
  margin: 2rem;
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-primary);
}

.temp-content h1 {
  color: var(--text-primary);
  margin-bottom: 1rem;
}

.temp-content p {
  color: var(--text-secondary);
  margin: 0.5rem 0;
}
</style>