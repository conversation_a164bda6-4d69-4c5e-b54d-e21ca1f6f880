<script setup>
import { onMounted } from 'vue';
import { useVoterStore } from './stores/voterStore.js';
import { useCategoriesStore } from './stores/categoriesStore.js';
import { useTheme } from './composables/useTheme.js';

// Import components (we'll create these next)
// import AppSidebar from './components/AppSidebar.vue';
// import AppToolbar from './components/AppToolbar.vue';
// import StatsCards from './components/StatsCards.vue';
// import FilterPanel from './components/FilterPanel.vue';
// import DataTable from './components/DataTable.vue';
// import VoterDetailPanel from './components/VoterDetailPanel.vue';
// import SettingsDashboard from './components/SettingsDashboard.vue';

// Initialize stores and composables
const voterStore = useVoterStore();
const categoriesStore = useCategoriesStore();
const { initializeTheme } = useTheme();

// Initialize the application
onMounted(async () => {
  initializeTheme();
  await categoriesStore.initialize();
  await voterStore.initialize();
});
</script>

<template>
  <div id="app">
    <!-- Temporary content while we build components -->
    <div class="temp-content">
      <h1>Voter Management Dashboard</h1>
      <p>Vue.js + Tauri conversion in progress...</p>
      <p>Total voters: {{ voterStore.totalVoters }}</p>
      <p>Communities: {{ categoriesStore.communities.length }}</p>
      <p>Religions: {{ categoriesStore.religions.length }}</p>
    </div>

    <!-- Future component structure:
    <AppSidebar />
    <main class="main-content">
      <AppToolbar />
      <StatsCards />
      <FilterPanel />
      <SettingsDashboard />
      <DataTable />
    </main>
    <VoterDetailPanel />
    -->
  </div>
</template>

<style>
/* Import CSS files */
@import './assets/normalize.css';
@import './assets/styles.css';

/* App-specific styles */
#app {
  width: 100%;
  height: 100vh;
  overflow: hidden;
}

/* Temporary styles for development */
.temp-content {
  padding: 2rem;
  text-align: center;
  background: var(--bg-secondary);
  margin: 2rem;
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-primary);
}

.temp-content h1 {
  color: var(--text-primary);
  margin-bottom: 1rem;
}

.temp-content p {
  color: var(--text-secondary);
  margin: 0.5rem 0;
}
</style>