<template>
  <div class="filter-options-container" :class="{ 'is-open': isOpen, 'animate-sweep': animateSweep }">
    <div class="filter-options">
      <div class="filter-header">
        <div class="filter-title">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <polygon points="22,3 2,3 10,12.46 10,19 14,21 14,12.46"></polygon>
          </svg>
          Filter Options
        </div>
        <button class="clear-filters" @click="clearAllFilters">
          Clear All
        </button>
      </div>
      
      <div class="filter-controls">
        <div class="filter-section">
          <!-- Gender Filter -->
          <div class="filter-group">
            <label for="gender-filter">Gender</label>
            <div class="select-wrapper">
              <select 
                id="gender-filter" 
                v-model="localFilters.gender"
                @change="updateFilters"
              >
                <option value="">All Genders</option>
                <option value="male">Male</option>
                <option value="female">Female</option>
                <option value="other">Other</option>
              </select>
            </div>
          </div>
          
          <!-- Age Range Filter -->
          <div class="filter-group">
            <label for="age-filter">Age Range</label>
            <div class="select-wrapper">
              <select 
                id="age-filter" 
                v-model="localFilters.ageRange"
                @change="updateFilters"
              >
                <option value="">All Ages</option>
                <option value="18-25">18-25</option>
                <option value="26-35">26-35</option>
                <option value="36-45">36-45</option>
                <option value="46-55">46-55</option>
                <option value="56-65">56-65</option>
                <option value="65+">65+</option>
              </select>
            </div>
          </div>
        </div>
        
        <div class="filter-section">
          <!-- Community Filter -->
          <div class="filter-group">
            <label for="community-filter">Community</label>
            <div class="select-wrapper">
              <select 
                id="community-filter" 
                v-model="localFilters.community"
                @change="updateFilters"
              >
                <option value="">All Communities</option>
                <option 
                  v-for="community in categoriesStore.communities" 
                  :key="community.id" 
                  :value="community.name"
                >
                  {{ community.name }}
                </option>
              </select>
            </div>
          </div>
          
          <!-- Religion Filter -->
          <div class="filter-group">
            <label for="religion-filter">Religion</label>
            <div class="select-wrapper">
              <select 
                id="religion-filter" 
                v-model="localFilters.religion"
                @change="updateFilters"
              >
                <option value="">All Religions</option>
                <option 
                  v-for="religion in categoriesStore.religions" 
                  :key="religion.id" 
                  :value="religion.name"
                >
                  {{ religion.name }}
                </option>
              </select>
            </div>
          </div>
        </div>
        
        <div class="filter-section">
          <!-- Economic Status Filter -->
          <div class="filter-group">
            <label for="economic-filter">Economic Status</label>
            <div class="select-wrapper">
              <select 
                id="economic-filter" 
                v-model="localFilters.economicStatus"
                @change="updateFilters"
              >
                <option value="">All Statuses</option>
                <option 
                  v-for="status in categoriesStore.economicStatuses" 
                  :key="status.id" 
                  :value="status.name"
                >
                  {{ status.name }}
                </option>
              </select>
            </div>
          </div>
          
          <!-- Search Filter -->
          <div class="filter-group">
            <label for="search-filter">Search</label>
            <input 
              id="search-filter"
              type="text" 
              placeholder="Search voters..."
              v-model="localFilters.searchTerm"
              @input="debouncedSearch"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, watch, onMounted } from 'vue';
import { useVoterStore } from '../stores/voterStore.js';
import { useCategoriesStore } from '../stores/categoriesStore.js';
import { debounce } from '../utils/utils.js';
import { DATA_CONFIG } from '../utils/config.js';

// Props
const props = defineProps({
  isOpen: {
    type: Boolean,
    default: false
  }
});

// Stores
const voterStore = useVoterStore();
const categoriesStore = useCategoriesStore();

// Local state
const animateSweep = ref(false);
const localFilters = reactive({
  gender: '',
  ageRange: '',
  community: '',
  religion: '',
  economicStatus: '',
  searchTerm: ''
});

// Debounced search function
const debouncedSearch = debounce(() => {
  updateFilters();
}, DATA_CONFIG.searchDebounceDelay);

// Methods
const updateFilters = () => {
  // Convert age range string to object if needed
  let ageRangeFilter = '';
  if (localFilters.ageRange) {
    const [min, max] = localFilters.ageRange.split('-');
    if (max === '+') {
      ageRangeFilter = { min: parseInt(min), max: 999 };
    } else {
      ageRangeFilter = { min: parseInt(min), max: parseInt(max) };
    }
  }
  
  // Update the store filters
  voterStore.updateFilters({
    gender: localFilters.gender,
    ageRange: ageRangeFilter,
    community: localFilters.community,
    religion: localFilters.religion,
    economicStatus: localFilters.economicStatus
  });
  
  // Update search term separately (it has its own debouncing)
  if (localFilters.searchTerm !== voterStore.searchTerm) {
    voterStore.setSearchTerm(localFilters.searchTerm);
  }
};

const clearAllFilters = () => {
  localFilters.gender = '';
  localFilters.ageRange = '';
  localFilters.community = '';
  localFilters.religion = '';
  localFilters.economicStatus = '';
  localFilters.searchTerm = '';
  
  voterStore.clearFilters();
  
  // Trigger sweep animation
  animateSweep.value = true;
  setTimeout(() => {
    animateSweep.value = false;
  }, 1500);
};

// Watch for changes in store filters to sync local state
watch(() => voterStore.filters, (newFilters) => {
  localFilters.gender = newFilters.gender || '';
  localFilters.community = newFilters.community || '';
  localFilters.religion = newFilters.religion || '';
  localFilters.economicStatus = newFilters.economicStatus || '';
  
  // Convert age range object back to string
  if (newFilters.ageRange && typeof newFilters.ageRange === 'object') {
    const { min, max } = newFilters.ageRange;
    if (max === 999) {
      localFilters.ageRange = `${min}+`;
    } else {
      localFilters.ageRange = `${min}-${max}`;
    }
  } else {
    localFilters.ageRange = '';
  }
}, { deep: true });

watch(() => voterStore.searchTerm, (newSearchTerm) => {
  localFilters.searchTerm = newSearchTerm || '';
});

// Initialize local filters from store on mount
onMounted(() => {
  const storeFilters = voterStore.filters;
  localFilters.gender = storeFilters.gender || '';
  localFilters.community = storeFilters.community || '';
  localFilters.religion = storeFilters.religion || '';
  localFilters.economicStatus = storeFilters.economicStatus || '';
  localFilters.searchTerm = voterStore.searchTerm || '';
  
  if (storeFilters.ageRange && typeof storeFilters.ageRange === 'object') {
    const { min, max } = storeFilters.ageRange;
    if (max === 999) {
      localFilters.ageRange = `${min}+`;
    } else {
      localFilters.ageRange = `${min}-${max}`;
    }
  }
});
</script>

<style scoped>
/* All styles are already defined in the main styles.css file */
/* This component uses the existing CSS classes without modification */
</style>
