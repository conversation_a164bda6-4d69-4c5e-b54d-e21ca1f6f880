<template>
  <div class="stats-container">
    <!-- Male Voters Card -->
    <div class="stat-card">
      <div class="stat-header">
        <div class="stat-label">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
            <circle cx="12" cy="7" r="4"></circle>
          </svg>
          Male Voters
        </div>
        <div class="stat-icon blue">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <circle cx="10" cy="14" r="8"></circle>
            <path d="m21,3 0,6 -6,0"></path>
            <path d="m21,3 -7.5,7.5"></path>
          </svg>
        </div>
      </div>
      <div class="stat-value">{{ voterStore.statistics.male.toLocaleString() }}</div>
      <div class="stat-change positive">
        <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <polyline points="23 6 13.5 15.5 8.5 10.5 1 18"></polyline>
          <polyline points="17 6 23 6 23 12"></polyline>
        </svg>
        {{ getMalePercentage() }}%
      </div>
    </div>

    <!-- Female Voters Card -->
    <div class="stat-card">
      <div class="stat-header">
        <div class="stat-label">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
            <circle cx="12" cy="7" r="4"></circle>
          </svg>
          Female Voters
        </div>
        <div class="stat-icon green">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <circle cx="12" cy="8" r="7"></circle>
            <path d="m12 15 0 6"></path>
            <path d="m9 18 6 0"></path>
          </svg>
        </div>
      </div>
      <div class="stat-value">{{ voterStore.statistics.female.toLocaleString() }}</div>
      <div class="stat-change positive">
        <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <polyline points="23 6 13.5 15.5 8.5 10.5 1 18"></polyline>
          <polyline points="17 6 23 6 23 12"></polyline>
        </svg>
        {{ getFemalePercentage() }}%
      </div>
    </div>

    <!-- Households Card -->
    <div class="stat-card">
      <div class="stat-header">
        <div class="stat-label">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
            <polyline points="9,22 9,12 15,12 15,22"></polyline>
          </svg>
          Households
        </div>
        <div class="stat-icon orange">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
            <polyline points="9,22 9,12 15,12 15,22"></polyline>
          </svg>
        </div>
      </div>
      <div class="stat-value">{{ voterStore.statistics.households.toLocaleString() }}</div>
      <div class="stat-change positive">
        <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <polyline points="23 6 13.5 15.5 8.5 10.5 1 18"></polyline>
          <polyline points="17 6 23 6 23 12"></polyline>
        </svg>
        {{ getHouseholdRatio() }}
      </div>
    </div>

    <!-- Total Voters Card -->
    <div class="stat-card">
      <div class="stat-header">
        <div class="stat-label">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
            <circle cx="9" cy="7" r="4"></circle>
            <path d="m22 21-3-3m0 0a2 2 0 0 0 0-4 2 2 0 0 0 0 4z"></path>
          </svg>
          Total Voters
        </div>
        <div class="stat-icon red">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path>
            <circle cx="9" cy="7" r="4"></circle>
            <path d="m22 21-3-3m0 0a2 2 0 0 0 0-4 2 2 0 0 0 0 4z"></path>
          </svg>
        </div>
      </div>
      <div class="stat-value">{{ voterStore.statistics.total.toLocaleString() }}</div>
      <div class="stat-change positive">
        <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <polyline points="23 6 13.5 15.5 8.5 10.5 1 18"></polyline>
          <polyline points="17 6 23 6 23 12"></polyline>
        </svg>
        100%
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import { useVoterStore } from '../stores/voterStore.js';

const voterStore = useVoterStore();

const getMalePercentage = () => {
  const total = voterStore.statistics.total;
  if (total === 0) return 0;
  return Math.round((voterStore.statistics.male / total) * 100);
};

const getFemalePercentage = () => {
  const total = voterStore.statistics.total;
  if (total === 0) return 0;
  return Math.round((voterStore.statistics.female / total) * 100);
};

const getHouseholdRatio = () => {
  const households = voterStore.statistics.households;
  const total = voterStore.statistics.total;
  if (households === 0 || total === 0) return '0:1';
  const ratio = Math.round(total / households * 10) / 10;
  return `${ratio}:1`;
};
</script>

<style scoped>
/* All styles are already defined in the main styles.css file */
/* This component uses the existing CSS classes without modification */
</style>
