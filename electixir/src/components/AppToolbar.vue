<template>
  <div class="toolbar">
    <div class="toolbar-left">
      <h1>Voter Management Dashboard</h1>
      <p>Manage and analyze voter data efficiently</p>
    </div>

    <div class="toolbar-right">
      <!-- Theme Toggle Button -->
      <button class="btn" @click="toggleTheme" :title="`Switch to ${theme.isDark ? 'light' : 'dark'} theme`">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <circle v-if="!theme.isDark" cx="12" cy="12" r="5"></circle>
          <line v-if="!theme.isDark" x1="12" y1="1" x2="12" y2="3"></line>
          <line v-if="!theme.isDark" x1="12" y1="21" x2="12" y2="23"></line>
          <line v-if="!theme.isDark" x1="4.22" y1="4.22" x2="5.64" y2="5.64"></line>
          <line v-if="!theme.isDark" x1="18.36" y1="18.36" x2="19.78" y2="19.78"></line>
          <line v-if="!theme.isDark" x1="1" y1="12" x2="3" y2="12"></line>
          <line v-if="!theme.isDark" x1="21" y1="12" x2="23" y2="12"></line>
          <line v-if="!theme.isDark" x1="4.22" y1="19.78" x2="5.64" y2="18.36"></line>
          <line v-if="!theme.isDark" x1="18.36" y1="5.64" x2="19.78" y2="4.22"></line>

          <path v-if="theme.isDark" d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z"></path>
        </svg>
      </button>

      <!-- Filter Toggle Button -->
      <button class="btn filter-btn" :class="{ 'is-active': showFilters }" @click="toggleFilters">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <polygon points="22,3 2,3 10,12.46 10,19 14,21 14,12.46"></polygon>
        </svg>
        Filters
      </button>

      <!-- Add Voter Button -->
      <button class="btn btn-primary" @click="showAddVoterModal">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <line x1="12" y1="5" x2="12" y2="19"></line>
          <line x1="5" y1="12" x2="19" y2="12"></line>
        </svg>
        Add Voter
      </button>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { useTheme } from '../composables/useTheme.js';

const theme = useTheme();
const showFilters = ref(false);

// Define emits
const emit = defineEmits(['toggle-filters']);

const toggleTheme = () => {
  theme.toggleTheme();
};

const toggleFilters = () => {
  showFilters.value = !showFilters.value;
  emit('toggle-filters');
};

const showAddVoterModal = () => {
  // Emit event or show modal for adding new voter
  console.log('Show add voter modal');
};
</script>

<style scoped>
/* All styles are already defined in the main styles.css file */
/* This component uses the existing CSS classes without modification */
</style>
