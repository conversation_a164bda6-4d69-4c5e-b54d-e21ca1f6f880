<template>
  <aside class="sidebar">
    <div class="sidebar-header">
      <!-- App branding/logo area -->
    </div>
    
    <div class="sidebar-content">
      <!-- Polling Stations Section -->
      <div class="sidebar-section">
        <div class="sidebar-title">Polling Stations</div>
        
        <div class="tree">
          <details open>
            <summary>
              <div class="checkbox-container">
                <input type="checkbox" id="all-stations" />
                <span class="checkmark"></span>
                <label class="checkbox-label" for="all-stations">All Stations</label>
              </div>
              <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <polyline points="9,18 15,12 9,6"></polyline>
              </svg>
            </summary>
            
            <ul>
              <li>
                <div class="checkbox-container child">
                  <input type="checkbox" id="station-1" />
                  <span class="checkmark"></span>
                  <label class="checkbox-label" for="station-1">Station 001 - Central School</label>
                </div>
              </li>
              <li>
                <div class="checkbox-container child">
                  <input type="checkbox" id="station-2" />
                  <span class="checkmark"></span>
                  <label class="checkbox-label" for="station-2">Station 002 - Community Hall</label>
                </div>
              </li>
              <li>
                <div class="checkbox-container child">
                  <input type="checkbox" id="station-3" />
                  <span class="checkmark"></span>
                  <label class="checkbox-label" for="station-3">Station 003 - Town Hall</label>
                </div>
              </li>
            </ul>
          </details>
        </div>
      </div>
      
      <!-- Management Section -->
      <div class="sidebar-section">
        <div class="sidebar-title">Management</div>
        
        <a href="#" class="sidebar-item" :class="{ 'is-active': currentView === 'dashboard' }" @click="setView('dashboard')">
          <div class="icon">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <rect x="3" y="3" width="7" height="7"></rect>
              <rect x="14" y="3" width="7" height="7"></rect>
              <rect x="14" y="14" width="7" height="7"></rect>
              <rect x="3" y="14" width="7" height="7"></rect>
            </svg>
          </div>
          Dashboard
        </a>
        
        <a href="#" class="sidebar-item" :class="{ 'is-active': currentView === 'reports' }" @click="setView('reports')">
          <div class="icon">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
              <polyline points="14,2 14,8 20,8"></polyline>
              <line x1="16" y1="13" x2="8" y2="13"></line>
              <line x1="16" y1="17" x2="8" y2="17"></line>
              <polyline points="10,9 9,9 8,9"></polyline>
            </svg>
          </div>
          Reports
        </a>
        
        <a href="#" class="sidebar-item" :class="{ 'is-active': currentView === 'settings' }" @click="setView('settings')">
          <div class="icon">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <circle cx="12" cy="12" r="3"></circle>
              <path d="m12 1 1.68 3.36L17 6.64l-1.32 3.36L12 11.32 8.32 10 7 6.64l3.36-1.32L12 1z"></path>
            </svg>
          </div>
          Settings
        </a>
      </div>
    </div>
    
    <div class="sidebar-footer">
      <div class="footer-summary">
        <small>{{ voterStore.totalVoters }} voters • {{ voterStore.statistics.households }} households</small>
      </div>
    </div>
  </aside>
</template>

<script setup>
import { ref } from 'vue';
import { useVoterStore } from '../stores/voterStore.js';

const voterStore = useVoterStore();
const currentView = ref('dashboard');

const setView = (view) => {
  currentView.value = view;
  // Emit event or use router navigation here
};
</script>

<style scoped>
/* All styles are already defined in the main styles.css file */
/* This component uses the existing CSS classes without modification */
</style>
