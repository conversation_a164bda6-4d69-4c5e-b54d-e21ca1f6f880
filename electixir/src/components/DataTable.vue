<template>
  <div class="data-table">
    <!-- Table Header -->
    <div class="table-header">
      <div class="table-title">Voter Data</div>
      <div class="table-controls">
        <!-- Search Input -->
        <div class="search-container">
          <svg class="search-icon" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <circle cx="11" cy="11" r="8"></circle>
            <path d="m21 21-4.35-4.35"></path>
          </svg>
          <input 
            type="text" 
            class="search-input" 
            placeholder="Search voters..."
            v-model="searchQuery"
            @input="handleSearch"
          />
        </div>
        
        <!-- Column Dropdown -->
        <div class="dropdown-container">
          <button class="btn" @click="toggleColumnsDropdown">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M3 12h18m-9-9v18"></path>
            </svg>
            Columns
          </button>
          <div class="dropdown-menu" :class="{ 'is-open': showColumnsDropdown }" id="columns-dropdown">
            <div 
              v-for="column in availableColumns" 
              :key="column.key"
              class="dropdown-item"
              :class="{ 'is-selected': visibleColumns.includes(column.key) }"
              @click="toggleColumn(column.key)"
            >
              <svg class="dropdown-item-icon" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <polyline points="20,6 9,17 4,12"></polyline>
              </svg>
              {{ column.label }}
            </div>
          </div>
        </div>
        
        <!-- Settings Dropdown -->
        <div class="dropdown-container">
          <button class="btn" @click="toggleSettingsDropdown">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <circle cx="12" cy="12" r="3"></circle>
              <path d="m12 1 1.68 3.36L17 6.64l-1.32 3.36L12 11.32 8.32 10 7 6.64l3.36-1.32L12 1z"></path>
            </svg>
          </button>
          <div class="dropdown-menu" :class="{ 'is-open': showSettingsDropdown }" id="settings-dropdown">
            <div class="dropdown-item" @click="exportData">
              <svg class="dropdown-item-icon" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                <polyline points="7,10 12,15 17,10"></polyline>
                <line x1="12" y1="15" x2="12" y2="3"></line>
              </svg>
              Export Data
            </div>
            <div class="dropdown-item" @click="importData">
              <svg class="dropdown-item-icon" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                <polyline points="17,8 12,3 7,8"></polyline>
                <line x1="12" y1="3" x2="12" y2="15"></line>
              </svg>
              Import Data
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Table Content -->
    <div class="table-content">
      <table>
        <thead>
          <tr>
            <th v-if="visibleColumns.includes('name')" @click="sortBy('name')">Name</th>
            <th v-if="visibleColumns.includes('age')" @click="sortBy('age')">Age</th>
            <th v-if="visibleColumns.includes('gender')" @click="sortBy('gender')">Gender</th>
            <th v-if="visibleColumns.includes('community')" @click="sortBy('community')">Community</th>
            <th v-if="visibleColumns.includes('religion')" @click="sortBy('religion')">Religion</th>
            <th v-if="visibleColumns.includes('economicStatus')" @click="sortBy('economicStatus')">Economic Status</th>
            <th v-if="visibleColumns.includes('pollingStation')" @click="sortBy('pollingStation')">Polling Station</th>
            <th v-if="visibleColumns.includes('actions')">Actions</th>
          </tr>
        </thead>
        <tbody>
          <tr 
            v-for="voter in voterStore.paginatedVoters" 
            :key="voter.id"
            @click="selectVoter(voter)"
            :class="{ 'selected': selectedVoterId === voter.id }"
          >
            <td v-if="visibleColumns.includes('name')" class="name-cell">{{ voter.name || 'N/A' }}</td>
            <td v-if="visibleColumns.includes('age')">{{ voter.age || 'N/A' }}</td>
            <td v-if="visibleColumns.includes('gender')">
              <span v-if="voter.gender" class="gender-badge" :class="voter.gender.toLowerCase()">
                {{ voter.gender }}
              </span>
              <span v-else>N/A</span>
            </td>
            <td v-if="visibleColumns.includes('community')">{{ voter.community || 'N/A' }}</td>
            <td v-if="visibleColumns.includes('religion')">{{ voter.religion || 'N/A' }}</td>
            <td v-if="visibleColumns.includes('economicStatus')">{{ voter.economicStatus || 'N/A' }}</td>
            <td v-if="visibleColumns.includes('pollingStation')">{{ voter.pollingStation || 'N/A' }}</td>
            <td v-if="visibleColumns.includes('actions')">
              <button class="action-menu" @click.stop="showVoterActions(voter)">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <circle cx="12" cy="12" r="1"></circle>
                  <circle cx="19" cy="12" r="1"></circle>
                  <circle cx="5" cy="12" r="1"></circle>
                </svg>
              </button>
            </td>
          </tr>
          
          <!-- Empty state -->
          <tr v-if="voterStore.paginatedVoters.length === 0">
            <td :colspan="visibleColumns.length" style="text-align: center; padding: 2rem; color: var(--text-tertiary);">
              {{ voterStore.loading ? 'Loading voters...' : 'No voters found' }}
            </td>
          </tr>
        </tbody>
      </table>
    </div>
    
    <!-- Table Footer -->
    <div class="table-footer">
      <div class="table-info">
        Showing {{ getDisplayRange() }} of {{ voterStore.filteredCount }} voters
      </div>
      <div class="pagination">
        <button 
          class="page-btn" 
          @click="previousPage" 
          :disabled="voterStore.currentPage <= 1"
        >
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <polyline points="15,18 9,12 15,6"></polyline>
          </svg>
        </button>
        
        <button 
          v-for="page in getVisiblePages()" 
          :key="page"
          class="page-btn"
          :class="{ 'is-active': page === voterStore.currentPage }"
          @click="goToPage(page)"
        >
          {{ page }}
        </button>
        
        <button 
          class="page-btn" 
          @click="nextPage" 
          :disabled="voterStore.currentPage >= voterStore.totalPages"
        >
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <polyline points="9,18 15,12 9,6"></polyline>
          </svg>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { useVoterStore } from '../stores/voterStore.js';
import { TABLE_CONFIG } from '../utils/config.js';
import { debounce, downloadFile } from '../utils/utils.js';

// Store
const voterStore = useVoterStore();

// Local state
const searchQuery = ref('');
const selectedVoterId = ref(null);
const showColumnsDropdown = ref(false);
const showSettingsDropdown = ref(false);
const visibleColumns = ref([...TABLE_CONFIG.defaultColumns]);
const availableColumns = TABLE_CONFIG.availableColumns;

// Methods
const handleSearch = debounce(() => {
  voterStore.setSearchTerm(searchQuery.value);
}, 300);

const selectVoter = (voter) => {
  selectedVoterId.value = voter.id;
  voterStore.selectVoter(voter);
};

const sortBy = (column) => {
  const currentSort = voterStore.sortConfig;
  const direction = currentSort.column === column && currentSort.direction === 'asc' ? 'desc' : 'asc';
  voterStore.setSortConfig(column, direction);
};

const toggleColumn = (columnKey) => {
  const index = visibleColumns.value.indexOf(columnKey);
  if (index > -1) {
    visibleColumns.value.splice(index, 1);
  } else {
    visibleColumns.value.push(columnKey);
  }
};

const toggleColumnsDropdown = () => {
  showColumnsDropdown.value = !showColumnsDropdown.value;
  showSettingsDropdown.value = false;
};

const toggleSettingsDropdown = () => {
  showSettingsDropdown.value = !showSettingsDropdown.value;
  showColumnsDropdown.value = false;
};

const exportData = () => {
  const csvData = voterStore.exportVoters();
  downloadFile(csvData, 'voters.csv', 'text/csv');
  showSettingsDropdown.value = false;
};

const importData = () => {
  // Create file input and trigger click
  const input = document.createElement('input');
  input.type = 'file';
  input.accept = '.csv';
  input.onchange = (e) => {
    const file = e.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        voterStore.importVoters(e.target.result);
      };
      reader.readAsText(file);
    }
  };
  input.click();
  showSettingsDropdown.value = false;
};

const showVoterActions = (voter) => {
  // Show context menu or actions for voter
  console.log('Show actions for voter:', voter);
};

// Pagination methods
const previousPage = () => {
  if (voterStore.currentPage > 1) {
    voterStore.setCurrentPage(voterStore.currentPage - 1);
  }
};

const nextPage = () => {
  if (voterStore.currentPage < voterStore.totalPages) {
    voterStore.setCurrentPage(voterStore.currentPage + 1);
  }
};

const goToPage = (page) => {
  voterStore.setCurrentPage(page);
};

const getVisiblePages = () => {
  const current = voterStore.currentPage;
  const total = voterStore.totalPages;
  const pages = [];
  
  // Show up to 5 pages around current page
  const start = Math.max(1, current - 2);
  const end = Math.min(total, current + 2);
  
  for (let i = start; i <= end; i++) {
    pages.push(i);
  }
  
  return pages;
};

const getDisplayRange = () => {
  const start = (voterStore.currentPage - 1) * voterStore.pageSize + 1;
  const end = Math.min(start + voterStore.pageSize - 1, voterStore.filteredCount);
  return `${start}-${end}`;
};

// Close dropdowns when clicking outside
const handleClickOutside = (event) => {
  if (!event.target.closest('.dropdown-container')) {
    showColumnsDropdown.value = false;
    showSettingsDropdown.value = false;
  }
};

onMounted(() => {
  document.addEventListener('click', handleClickOutside);
  searchQuery.value = voterStore.searchTerm;
});

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside);
});
</script>

<style scoped>
/* All styles are already defined in the main styles.css file */
/* This component uses the existing CSS classes without modification */
</style>
