Below CORE dataset all labels contain values its mandatory. I have CSV for this in exaclty the same names provided.
```
name
relationship_type (<PERSON>, <PERSON>, <PERSON><PERSON>, Others)
relationship_name
gender ('Male', 'Female', 'Other')
birth_year
epic_number
house_number (can be a house number or address)
polling_station
section
```
NOTE: on the above. I have many Polling station around 100
Inside eache Polling station are several Section, users are assigned to this section are there are approximately 700 users per section.

Around this core data are other dataset that admin enters manually and as such are optional.

Extended Contact Information
```
phone
email
facebook
instagram
twitter
```

Voter status
```
status ('Active', 'Expired', 'Shifted', 'Duplicate', 'Missing', 'Disqualified')
```
NOTE: on the above. User marked Duplicate or Disqualified should be delete from record.

Political Information
```
supporter_status ('Strong Supporter', 'Potential Supporter', 'Undecided', 'Opposed')
```

Demographics
```
education
occupation
community
religion
economic_status
custom_notes
```
NOTE: For community, religion, economic_status. I envision a settings/admin dashboard. That shows a select box based on the above. When I select a value I get to enter/input detail e.g. for Religion I would add Hindu then click Add. Its added to a list that can be edited or deleted. If I delete it the values goes back to Null. Tldr No prefilled values users gets to enter their own custom data.

Transaction Record (Simple)
```
Date
Purpose
Ammount
```
NOTE: I will store money give to benificery.

Login System: For Admin, Users and Data entry

INDEX Epic, Name, voter status, voters_relationship (relationship_name, relationship_type), Transaction (What you feel will be good).